<template>
  <div class="upload-avatar-demo">
    <div class="demo-section">
      <h3>头像上传组件演示 (Vue-Cropper版本)</h3>
      <p class="demo-description">
        使用 vue-cropper 重构的头像上传组件，提供更专业的图片裁剪功能
      </p>
      
      <div class="demo-content">
        <div class="demo-item">
          <h4>默认尺寸 (100px)</h4>
          <UploadAvatar 
            v-model:value="avatar1" 
            @success="handleSuccess"
          />
          <p v-if="avatar1" class="result-text">当前头像: {{ avatar1 }}</p>
        </div>

        <div class="demo-item">
          <h4>大尺寸 (150px)</h4>
          <UploadAvatar 
            v-model:value="avatar2" 
            :size="150"
            @success="handleSuccess"
          />
          <p v-if="avatar2" class="result-text">当前头像: {{ avatar2 }}</p>
        </div>

        <div class="demo-item">
          <h4>禁用状态</h4>
          <UploadAvatar 
            v-model:value="avatar3" 
            :disabled="true"
          />
        </div>
      </div>

      <div class="feature-list">
        <h4>Vue-Cropper 功能特性</h4>
        <ul>
          <li>✅ 专业的图片裁剪工具</li>
          <li>✅ 支持鼠标滚轮缩放</li>
          <li>✅ 拖拽调整裁剪区域</li>
          <li>✅ 左转/右转旋转功能</li>
          <li>✅ 放大/缩小精确控制</li>
          <li>✅ 实时预览裁剪效果</li>
          <li>✅ 固定1:1比例裁剪</li>
          <li>✅ 高质量图片输出</li>
          <li>✅ 响应式设计</li>
          <li>✅ 内存管理优化</li>
        </ul>
      </div>

      <div class="usage-guide">
        <h4>使用说明</h4>
        <div class="guide-steps">
          <div class="step">
            <span class="step-number">1</span>
            <div class="step-content">
              <strong>选择图片</strong>
              <p>点击头像区域选择要上传的图片文件</p>
            </div>
          </div>
          <div class="step">
            <span class="step-number">2</span>
            <div class="step-content">
              <strong>调整裁剪区域</strong>
              <p>拖拽裁剪框调整位置和大小</p>
            </div>
          </div>
          <div class="step">
            <span class="step-number">3</span>
            <div class="step-content">
              <strong>精细调整</strong>
              <p>使用控制按钮进行缩放和旋转</p>
            </div>
          </div>
          <div class="step">
            <span class="step-number">4</span>
            <div class="step-content">
              <strong>确认上传</strong>
              <p>预览满意后点击确认上传按钮</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import UploadAvatar from './UploadAvatar.vue';
import type { FileUploadResponse } from './types';

const avatar1 = ref<string>('');
const avatar2 = ref<string>('');
const avatar3 = ref<string>('');

const handleSuccess = (response: FileUploadResponse) => {
  message.success(`头像上传成功: ${response.fileName}`);
  console.log('Upload success:', response);
};
</script>

<style scoped>
.upload-avatar-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-section h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.demo-description {
  color: #666;
  margin-bottom: 24px;
  font-size: 14px;
}

.demo-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.demo-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.demo-item h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.result-text {
  margin-top: 12px;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}

.feature-list {
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
  margin-bottom: 24px;
}

.feature-list h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.feature-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 8px;
}

.feature-list li {
  padding: 8px 0;
  color: #666;
  font-size: 14px;
}

.usage-guide {
  border-top: 1px solid #f0f0f0;
  padding-top: 24px;
}

.usage-guide h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.guide-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #1890ff;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content strong {
  display: block;
  color: #333;
  font-size: 14px;
  margin-bottom: 4px;
}

.step-content p {
  margin: 0;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .demo-content {
    grid-template-columns: 1fr;
  }
  
  .feature-list ul {
    grid-template-columns: 1fr;
  }
  
  .guide-steps {
    grid-template-columns: 1fr;
  }
}
</style>
