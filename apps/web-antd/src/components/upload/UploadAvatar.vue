<script setup lang="ts">
import type { FileUploadResponse, ImageUploadResponse } from './types';

import { nextTick, ref, computed } from 'vue';

import { message, Modal, Upload, But<PERSON>, Slider } from 'ant-design-vue';
import { RedoOutlined, UndoOutlined, ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons-vue';
import { VueCropper } from 'vue-cropper';
import 'vue-cropper/dist/index.css';

import { FileApi } from '#/api/common/file';

interface Props {
  value?: string;
  /** 头像尺寸 */
  size?: number;
  /** 是否禁用 */
  disabled?: boolean;
}

interface Emits {
  (e: 'success', data: FileUploadResponse): void;
  (e: 'update:value', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  size: 100,
  disabled: false,
});
const emit = defineEmits<Emits>();

const loading = ref(false);
const imageUrl = ref<string>('');

// 裁剪相关状态
const showCropModal = ref(false);
const cropper = ref<InstanceType<typeof VueCropper>>();
const originalFile = ref<File>();
const previewUrl = ref<string>('');

// vue-cropper 配置选项
const cropperOptions = ref({
  img: '', // 裁剪图片的地址
  outputSize: 1, // 裁剪生成图片的质量
  outputType: 'jpeg', // 裁剪生成图片的格式
  canScale: true, // 图片是否允许滚轮缩放
  autoCrop: true, // 是否默认生成截图框
  autoCropWidth: 200, // 默认生成截图框宽度
  autoCropHeight: 200, // 默认生成截图框高度
  fixedBox: false, // 固定截图框大小 不允许改变
  fixed: true, // 是否开启截图框宽高固定比例
  fixedNumber: [1, 1], // 截图框的宽高比例
  full: true, // 是否输出原图比例的截图
  canMoveBox: true, // 截图框能否拖动
  original: false, // 上传图片按照原始比例渲染
  centerBox: true, // 截图框是否被限制在图片里面
  infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
  high: true, // 是否按照设备的dpr 输出等比例图片
  enlarge: 1, // 图片根据截图框输出比例倍数
  mode: 'contain' as const, // 图片默认渲染方式
});

// 计算属性
const avatarStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
}));

// 自定义上传函数
const customRequest = (options: any) => {
  const { file } = options;

  // 文件类型验证
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件');
    return;
  }

  // 文件大小验证 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    message.error('图片大小不能超过5MB');
    return;
  }

  originalFile.value = file;

  // 创建图片预览并显示裁剪弹窗
  const reader = new FileReader();
  reader.addEventListener('load', (e) => {
    const result = e.target?.result as string;
    cropperOptions.value.img = result;
    showCropModal.value = true;

    // 等待组件渲染完成后初始化预览
    nextTick(() => {
      updatePreview();
    });
  });
  reader.readAsDataURL(file);
};

// 更新预览
const updatePreview = () => {
  if (!cropper.value) return;

  cropper.value.getCropBlob((blob: Blob) => {
    if (blob) {
      previewUrl.value = URL.createObjectURL(blob);
    }
  });
};

// vue-cropper 控制方法
const handleZoomIn = () => {
  cropper.value?.changeScale(1);
  updatePreview();
};

const handleZoomOut = () => {
  cropper.value?.changeScale(-1);
  updatePreview();
};

const handleRotateLeft = () => {
  cropper.value?.rotateLeft();
  updatePreview();
};

const handleRotateRight = () => {
  cropper.value?.rotateRight();
  updatePreview();
};

const handleReset = () => {
  cropper.value?.refresh();
  updatePreview();
};

// 实时裁剪回调
const handleRealTime = () => {
  updatePreview();
};

// 确认裁剪
const confirmCrop = async () => {
  if (!originalFile.value || !cropper.value) return;

  try {
    loading.value = true;

    // 获取裁剪后的图片 blob
    cropper.value.getCropBlob(async (blob: Blob) => {
      if (!blob) {
        message.error('裁剪失败，请重试');
        return;
      }

      const croppedFile = new File([blob], originalFile.value!.name, {
        type: originalFile.value!.type,
      });

      try {
        const response = await FileApi.uploadImage(croppedFile);
        if (response.fileUrl) {
          imageUrl.value = response.fileUrl;
          emit('update:value', response.fileUrl);

          const fileResponse: ImageUploadResponse = {
            fileName: response.fileName,
            fileSize: String(response.fileSize || ''),
            fileUrl: response.fileUrl,
            previewUrl: response.previewUrl || response.fileUrl,
            fileType: response.fileType,
          };
          emit('success', fileResponse);
          message.success('头像上传成功');
          showCropModal.value = false;
        }
      } catch (error) {
        console.error('Upload failed:', error);
        message.error('头像上传失败，请重试');
      } finally {
        loading.value = false;
      }
    });
  } catch (error) {
    console.error('Crop failed:', error);
    message.error('裁剪失败，请重试');
    loading.value = false;
  }
};

// 取消裁剪
const cancelCrop = () => {
  showCropModal.value = false;
  originalFile.value = undefined;
  cropperOptions.value.img = '';
  previewUrl.value = '';

  // 清理预览 URL
  if (previewUrl.value && previewUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(previewUrl.value);
  }
};
</script>

<template>
  <div class="upload-avatar">
    <Upload
      :custom-request="customRequest"
      :disabled="props.disabled"
      :show-upload-list="false"
      accept="image/*"
      class="avatar-uploader"
    >
      <div :style="avatarStyle" class="upload-area" :class="{ disabled: props.disabled }">
        <div v-if="imageUrl" class="avatar-preview">
          <img :src="imageUrl" alt="avatar" />
          <div class="avatar-overlay">
            <div class="overlay-icon">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div class="overlay-text">更换头像</div>
          </div>
        </div>
        <div v-else class="upload-placeholder">
          <div class="upload-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </div>
          <div class="upload-text">上传头像</div>
        </div>
      </div>
    </Upload>

    <!-- 裁剪弹窗 -->
    <Modal
      v-model:open="showCropModal"
      title="编辑头像"
      :width="900"
      :confirm-loading="loading"
      ok-text="确认上传"
      cancel-text="取消"
      @ok="confirmCrop"
      @cancel="cancelCrop"
    >
      <div class="crop-modal-content">
        <!-- 编辑区域 -->
        <div class="crop-container">
          <div class="crop-area">
            <div class="crop-canvas-wrapper">
              <VueCropper
                ref="cropper"
                :img="cropperOptions.img"
                :output-size="cropperOptions.outputSize"
                :output-type="cropperOptions.outputType"
                :can-scale="cropperOptions.canScale"
                :auto-crop="cropperOptions.autoCrop"
                :auto-crop-width="cropperOptions.autoCropWidth"
                :auto-crop-height="cropperOptions.autoCropHeight"
                :fixed-box="cropperOptions.fixedBox"
                :fixed="cropperOptions.fixed"
                :fixed-number="cropperOptions.fixedNumber"
                :full="cropperOptions.full"
                :can-move-box="cropperOptions.canMoveBox"
                :original="cropperOptions.original"
                :center-box="cropperOptions.centerBox"
                :info-true="cropperOptions.infoTrue"
                :high="cropperOptions.high"
                :enlarge="cropperOptions.enlarge"
                :mode="cropperOptions.mode"
                @real-time="handleRealTime"
                class="vue-cropper"
              />
            </div>
            <div class="crop-tips">
              <p>• 拖拽选框调整裁剪区域</p>
              <p>• 使用鼠标滚轮缩放图片</p>
              <p>• 点击按钮进行旋转和缩放操作</p>
            </div>
          </div>

          <div class="preview-area">
            <div class="preview-title">头像预览</div>
            <div class="preview-circle">
              <img v-if="previewUrl" :src="previewUrl" alt="预览" />
              <div v-else class="preview-placeholder">预览区域</div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
              <div class="control-actions">
                <Button size="small" @click="handleZoomIn">
                  <template #icon>
                    <ZoomInOutlined />
                  </template>
                  放大
                </Button>
                <Button size="small" @click="handleZoomOut">
                  <template #icon>
                    <ZoomOutOutlined />
                  </template>
                  缩小
                </Button>
                <Button size="small" @click="handleRotateLeft">
                  <template #icon>
                    <UndoOutlined />
                  </template>
                  左转
                </Button>
                <Button size="small" @click="handleRotateRight">
                  <template #icon>
                    <RedoOutlined />
                  </template>
                  右转
                </Button>
                <Button size="small" @click="handleReset">
                  重置
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.upload-avatar {
  display: inline-block;
}

.avatar-uploader {
  display: block;
}

.upload-area {
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  position: relative;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
  transform: scale(1.02);
}

.upload-area.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-area.disabled:hover {
  border-color: #d9d9d9;
  background: #fafafa;
  transform: none;
}

.avatar-preview {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
}

.avatar-preview:hover .avatar-overlay {
  opacity: 1;
}

.overlay-icon {
  margin-bottom: 4px;
}

.overlay-text {
  font-size: 12px;
  font-weight: 500;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  text-align: center;
}

.upload-icon {
  margin-bottom: 8px;
  color: #1890ff;
}

.upload-text {
  font-size: 14px;
  font-weight: 500;
}

/* 裁剪弹窗样式 */
.crop-modal-content {
  padding: 16px 0;
}

.crop-container {
  display: flex;
  gap: 24px;
  min-height: 400px;
}

.crop-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.crop-canvas-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  min-height: 400px;
  background: #f8f9fa;
  border-radius: 8px;
}

.vue-cropper {
  width: 100%;
  height: 400px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.crop-tips {
  margin-top: 16px;
  padding: 12px;
}

.crop-tips p {
  margin: 4px 0;
  font-size: 13px;
  color: #666;
}

.preview-area {
  width: 220px;
  text-align: center;
  display: flex;
  flex-direction: column;
}

.preview-title {
  margin-bottom: 16px;
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.preview-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e9ecef;
  margin: 0 auto 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.preview-circle img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  background: #f5f5f5;
}

.control-panel {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.control-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.control-actions .ant-btn {
  flex: 1;
  min-width: 70px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .crop-container {
    flex-direction: column;
    gap: 16px;
  }

  .preview-area {
    width: 100%;
  }

  .control-actions {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
