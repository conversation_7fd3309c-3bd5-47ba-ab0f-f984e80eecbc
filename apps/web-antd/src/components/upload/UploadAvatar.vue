<script setup lang="ts">
import type { FileUploadResponse, ImageUploadResponse } from './types';

import { nextTick, ref } from 'vue';

import { message, Modal, Upload } from 'ant-design-vue';

import { FileApi } from '#/api/common/file';

interface Props {
  value?: string;
}

interface Emits {
  (e: 'success', data: FileUploadResponse): void;
  (e: 'update:value', value: string): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

const loading = ref(false);
const imageUrl = ref<string>('');

// 裁剪相关状态
const showCropModal = ref(false);
const cropCanvas = ref<HTMLCanvasElement>();
const previewCanvas = ref<HTMLCanvasElement>();
const cropContext = ref<CanvasRenderingContext2D>();
const originalImage = ref<HTMLImageElement>();
const cropArea = ref({ x: 0, y: 0, width: 200, height: 200 });
const isDragging = ref(false);
const dragStart = ref({ x: 0, y: 0 });
const originalFile = ref<File>();

// 自定义上传函数
const customRequest = (options: any) => {
  const { file } = options;
  originalFile.value = file;

  // 创建图片预览并显示裁剪弹窗
  const reader = new FileReader();
  reader.addEventListener('load', (e) => {
    const img = new Image();
    img.addEventListener('load', () => {
      originalImage.value = img;
      showCropModal.value = true;
      nextTick(() => {
        initCropCanvas();
      });
    });
    img.src = e.target?.result as string;
  });
  reader.readAsDataURL(file);
};

// 初始化裁剪画布
const initCropCanvas = () => {
  const canvas = cropCanvas.value;
  if (!canvas || !originalImage.value) return;

  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  cropContext.value = ctx;

  // 设置画布尺寸
  const maxSize = 400;
  const img = originalImage.value;
  let { width, height } = img;

  // 按比例缩放
  if (width > maxSize || height > maxSize) {
    const ratio = Math.min(maxSize / width, maxSize / height);
    width *= ratio;
    height *= ratio;
  }

  canvas.width = width;
  canvas.height = height;

  // 初始化裁剪区域
  const size = Math.min(width, height, 200);
  cropArea.value = {
    x: (width - size) / 2,
    y: (height - size) / 2,
    width: size,
    height: size,
  };

  drawCanvas();
};

// 绘制画布
const drawCanvas = () => {
  const canvas = cropCanvas.value;
  const ctx = cropContext.value;
  const img = originalImage.value;
  if (!canvas || !ctx || !img) return;

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 绘制原图
  ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

  // 绘制遮罩
  ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 清除裁剪区域的遮罩
  ctx.globalCompositeOperation = 'destination-out';
  ctx.fillRect(
    cropArea.value.x,
    cropArea.value.y,
    cropArea.value.width,
    cropArea.value.height,
  );

  // 重新绘制裁剪区域的图片
  ctx.globalCompositeOperation = 'source-over';
  ctx.drawImage(
    img,
    cropArea.value.x,
    cropArea.value.y,
    cropArea.value.width,
    cropArea.value.height,
    cropArea.value.x,
    cropArea.value.y,
    cropArea.value.width,
    cropArea.value.height,
  );

  // 绘制裁剪框边框
  ctx.strokeStyle = '#1890ff';
  ctx.lineWidth = 2;
  ctx.strokeRect(
    cropArea.value.x,
    cropArea.value.y,
    cropArea.value.width,
    cropArea.value.height,
  );

  updatePreview();
};

// 更新预览
const updatePreview = () => {
  const previewCtx = previewCanvas.value?.getContext('2d');
  const img = originalImage.value;
  if (!previewCtx || !img) return;

  previewCtx.clearRect(0, 0, 100, 100);

  // 创建圆形裁剪路径
  previewCtx.save();
  previewCtx.beginPath();
  previewCtx.arc(50, 50, 50, 0, Math.PI * 2);
  previewCtx.clip();

  // 绘制裁剪后的图片
  previewCtx.drawImage(
    img,
    cropArea.value.x,
    cropArea.value.y,
    cropArea.value.width,
    cropArea.value.height,
    0,
    0,
    100,
    100,
  );

  previewCtx.restore();
};

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  const canvas = cropCanvas.value;
  if (!canvas) return;

  const rect = canvas.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  // 检查是否在裁剪区域内
  if (
    x >= cropArea.value.x &&
    x <= cropArea.value.x + cropArea.value.width &&
    y >= cropArea.value.y &&
    y <= cropArea.value.y + cropArea.value.height
  ) {
    isDragging.value = true;
    dragStart.value = { x: x - cropArea.value.x, y: y - cropArea.value.y };
  }
};

// 拖拽中
const onDrag = (e: MouseEvent) => {
  if (!isDragging.value) return;

  const canvas = cropCanvas.value;
  if (!canvas) return;

  const rect = canvas.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  cropArea.value.x = Math.max(
    0,
    Math.min(x - dragStart.value.x, canvas.width - cropArea.value.width),
  );
  cropArea.value.y = Math.max(
    0,
    Math.min(y - dragStart.value.y, canvas.height - cropArea.value.height),
  );

  drawCanvas();
};

// 结束拖拽
const endDrag = () => {
  isDragging.value = false;
};

// 确认裁剪
const confirmCrop = async () => {
  if (!originalFile.value || !originalImage.value) return;

  try {
    loading.value = true;

    // 创建裁剪后的图片
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = cropArea.value.width;
    canvas.height = cropArea.value.height;

    ctx.drawImage(
      originalImage.value,
      cropArea.value.x,
      cropArea.value.y,
      cropArea.value.width,
      cropArea.value.height,
      0,
      0,
      cropArea.value.width,
      cropArea.value.height,
    );

    // 转换为Blob
    canvas.toBlob(async (blob) => {
      if (!blob) return;

      const croppedFile = new File([blob], originalFile.value!.name, {
        type: originalFile.value!.type,
      });

      try {
        const response = await FileApi.uploadImage(croppedFile);
        if (response.fileUrl) {
          imageUrl.value = response.fileUrl;
          emit('update:value', response.fileUrl);

          const fileResponse: ImageUploadResponse = {
            fileName: response.fileName,
            fileSize: String(response.fileSize || ''),
            fileUrl: response.fileUrl,
            fileType: response.fileType,
          };
          emit('success', fileResponse);
          message.success('头像上传成功');
          showCropModal.value = false;
        }
      } catch (error) {
        console.error('Upload failed:', error);
        message.error('头像上传失败');
      }
    }, originalFile.value.type);
  } finally {
    loading.value = false;
  }
};

// 取消裁剪
const cancelCrop = () => {
  showCropModal.value = false;
  originalFile.value = undefined;
  originalImage.value = undefined;
};
</script>

<template>
  <div class="upload-avatar">
    <Upload
      :custom-request="customRequest"
      :show-upload-list="false"
      accept="image/*"
      class="avatar-uploader"
    >
      <div class="upload-area">
        <div v-if="imageUrl" class="avatar-preview">
          <img :src="imageUrl" alt="avatar" />
        </div>
        <div v-else class="upload-placeholder">
          <div class="upload-icon">+</div>
          <div class="upload-text">上传头像</div>
        </div>
      </div>
    </Upload>

    <!-- 裁剪弹窗 -->
    <Modal
      v-model:open="showCropModal"
      title="裁剪头像"
      :width="600"
      @ok="confirmCrop"
      @cancel="cancelCrop"
    >
      <div class="crop-container">
        <div class="crop-area">
          <canvas
            ref="cropCanvas"
            @mousedown="startDrag"
            @mousemove="onDrag"
            @mouseup="endDrag"
            @mouseleave="endDrag"
          ></canvas>
        </div>
        <div class="preview-area">
          <div class="preview-title">预览</div>
          <div class="preview-circle">
            <canvas ref="previewCanvas" width="100" height="100"></canvas>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.upload-avatar {
  display: inline-block;
}

.avatar-uploader {
  display: block;
}

.upload-area {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border: 1px solid #d9d9d9;
}

.upload-area:hover {
  border-color: #1890ff;
}

.avatar-preview {
  width: 100%;
  height: 100%;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
}

.crop-container {
  display: flex;
  gap: 20px;
}

.crop-area {
  flex: 1;
}

.crop-area canvas {
  border: 1px solid #d9d9d9;
  cursor: move;
}

.preview-area {
  width: 120px;
  text-align: center;
}

.preview-title {
  margin-bottom: 10px;
  font-weight: 500;
}

.preview-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  margin: 0 auto;
}

.preview-circle canvas {
  display: block;
}
</style>
