<script setup lang="ts">
import type { FileUploadResponse, ImageUploadResponse } from './types';

import { nextTick, ref, computed } from 'vue';

import { message, Modal, Upload, But<PERSON>, Slider } from 'ant-design-vue';

import { FileApi } from '#/api/common/file';

interface Props {
  value?: string;
  /** 头像尺寸 */
  size?: number;
  /** 是否禁用 */
  disabled?: boolean;
}

interface Emits {
  (e: 'success', data: FileUploadResponse): void;
  (e: 'update:value', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  size: 100,
  disabled: false,
});
const emit = defineEmits<Emits>();

const loading = ref(false);
const imageUrl = ref<string>('');

// 裁剪相关状态
const showCropModal = ref(false);
const cropCanvas = ref<HTMLCanvasElement>();
const previewCanvas = ref<HTMLCanvasElement>();
const cropContext = ref<CanvasRenderingContext2D>();
const originalImage = ref<HTMLImageElement>();
const cropArea = ref({ x: 0, y: 0, width: 200, height: 200 });
const isDragging = ref(false);
const dragStart = ref({ x: 0, y: 0 });
const originalFile = ref<File>();

// 缩放相关状态
const scale = ref(1);
const minScale = ref(0.1);
const maxScale = ref(3);
const rotation = ref(0);

// 计算属性
const avatarStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
}));

// 自定义上传函数
const customRequest = (options: any) => {
  const { file } = options;

  // 文件类型验证
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件');
    return;
  }

  // 文件大小验证 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    message.error('图片大小不能超过5MB');
    return;
  }

  originalFile.value = file;

  // 重置状态
  scale.value = 1;
  rotation.value = 0;

  // 创建图片预览并显示裁剪弹窗
  const reader = new FileReader();
  reader.addEventListener('load', (e) => {
    const img = new Image();
    img.addEventListener('load', () => {
      originalImage.value = img;
      showCropModal.value = true;
      nextTick(() => {
        initCropCanvas();
      });
    });
    img.src = e.target?.result as string;
  });
  reader.readAsDataURL(file);
};

// 初始化裁剪画布
const initCropCanvas = () => {
  const canvas = cropCanvas.value;
  if (!canvas || !originalImage.value) return;

  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  cropContext.value = ctx;

  // 设置画布尺寸 - 增大画布以提供更好的编辑体验
  const maxSize = 500;
  const img = originalImage.value;
  let { width, height } = img;

  // 计算缩放比例以适应画布
  const ratio = Math.min(maxSize / width, maxSize / height);
  width *= ratio;
  height *= ratio;

  canvas.width = width;
  canvas.height = height;

  // 设置缩放范围
  minScale.value = Math.max(0.1, 200 / Math.max(width, height));
  maxScale.value = 3;

  // 初始化裁剪区域 - 增大默认裁剪区域
  const size = Math.min(width, height, 250);
  cropArea.value = {
    x: (width - size) / 2,
    y: (height - size) / 2,
    width: size,
    height: size,
  };

  drawCanvas();
};

// 绘制画布
const drawCanvas = () => {
  const canvas = cropCanvas.value;
  const ctx = cropContext.value;
  const img = originalImage.value;
  if (!canvas || !ctx || !img) return;

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 保存当前状态
  ctx.save();

  // 应用变换
  const centerX = canvas.width / 2;
  const centerY = canvas.height / 2;

  ctx.translate(centerX, centerY);
  ctx.scale(scale.value, scale.value);
  ctx.rotate((rotation.value * Math.PI) / 180);
  ctx.translate(-centerX, -centerY);

  // 绘制原图
  ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

  // 恢复状态
  ctx.restore();

  // 绘制遮罩
  ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 清除裁剪区域的遮罩
  ctx.globalCompositeOperation = 'destination-out';
  ctx.fillRect(
    cropArea.value.x,
    cropArea.value.y,
    cropArea.value.width,
    cropArea.value.height,
  );

  // 重新绘制裁剪区域的图片
  ctx.globalCompositeOperation = 'source-over';
  ctx.save();

  // 裁剪区域应用变换
  ctx.translate(centerX, centerY);
  ctx.scale(scale.value, scale.value);
  ctx.rotate((rotation.value * Math.PI) / 180);
  ctx.translate(-centerX, -centerY);

  ctx.drawImage(
    img,
    cropArea.value.x / scale.value,
    cropArea.value.y / scale.value,
    cropArea.value.width / scale.value,
    cropArea.value.height / scale.value,
    cropArea.value.x,
    cropArea.value.y,
    cropArea.value.width,
    cropArea.value.height,
  );

  ctx.restore();

  // 绘制裁剪框边框和控制点
  ctx.strokeStyle = '#1890ff';
  ctx.lineWidth = 2;
  ctx.strokeRect(
    cropArea.value.x,
    cropArea.value.y,
    cropArea.value.width,
    cropArea.value.height,
  );

  // 绘制角落控制点
  const controlSize = 8;
  ctx.fillStyle = '#1890ff';
  const corners = [
    { x: cropArea.value.x - controlSize / 2, y: cropArea.value.y - controlSize / 2 },
    { x: cropArea.value.x + cropArea.value.width - controlSize / 2, y: cropArea.value.y - controlSize / 2 },
    { x: cropArea.value.x - controlSize / 2, y: cropArea.value.y + cropArea.value.height - controlSize / 2 },
    { x: cropArea.value.x + cropArea.value.width - controlSize / 2, y: cropArea.value.y + cropArea.value.height - controlSize / 2 },
  ];

  corners.forEach(corner => {
    ctx.fillRect(corner.x, corner.y, controlSize, controlSize);
  });

  updatePreview();
};

// 更新预览
const updatePreview = () => {
  const previewCtx = previewCanvas.value?.getContext('2d');
  const img = originalImage.value;
  if (!previewCtx || !img) return;

  const previewSize = 150; // 增大预览尺寸
  previewCtx.clearRect(0, 0, previewSize, previewSize);

  // 创建圆形裁剪路径
  previewCtx.save();
  previewCtx.beginPath();
  previewCtx.arc(previewSize / 2, previewSize / 2, previewSize / 2, 0, Math.PI * 2);
  previewCtx.clip();

  // 应用变换到预览
  const centerX = previewSize / 2;
  const centerY = previewSize / 2;

  previewCtx.translate(centerX, centerY);
  previewCtx.scale(scale.value, scale.value);
  previewCtx.rotate((rotation.value * Math.PI) / 180);
  previewCtx.translate(-centerX, -centerY);

  // 绘制裁剪后的图片
  previewCtx.drawImage(
    img,
    cropArea.value.x / scale.value,
    cropArea.value.y / scale.value,
    cropArea.value.width / scale.value,
    cropArea.value.height / scale.value,
    0,
    0,
    previewSize,
    previewSize,
  );

  previewCtx.restore();

  // 更新不同尺寸的预览
  updateSizePreviews();
};

// 更新不同尺寸的预览
const updateSizePreviews = () => {
  const img = originalImage.value;
  if (!img) return;

  const sizes = [
    { size: 40, selector: '.size-large canvas' },
    { size: 32, selector: '.size-medium canvas' },
    { size: 24, selector: '.size-small canvas' },
  ];

  sizes.forEach(({ size, selector }) => {
    const canvas = document.querySelector(selector) as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = size;
    canvas.height = size;
    ctx.clearRect(0, 0, size, size);

    // 创建圆形裁剪路径
    ctx.save();
    ctx.beginPath();
    ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
    ctx.clip();

    // 应用变换
    const centerX = size / 2;
    const centerY = size / 2;

    ctx.translate(centerX, centerY);
    ctx.scale(scale.value, scale.value);
    ctx.rotate((rotation.value * Math.PI) / 180);
    ctx.translate(-centerX, -centerY);

    // 绘制图片
    ctx.drawImage(
      img,
      cropArea.value.x / scale.value,
      cropArea.value.y / scale.value,
      cropArea.value.width / scale.value,
      cropArea.value.height / scale.value,
      0,
      0,
      size,
      size,
    );

    ctx.restore();
  });
};

// 鼠标滚轮缩放
const handleWheel = (e: WheelEvent) => {
  e.preventDefault();
  const delta = e.deltaY > 0 ? -0.1 : 0.1;
  const newScale = Math.max(minScale.value, Math.min(maxScale.value, scale.value + delta));
  scale.value = newScale;
  drawCanvas();
};

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  const canvas = cropCanvas.value;
  if (!canvas) return;

  const rect = canvas.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  // 检查是否在裁剪区域内
  if (
    x >= cropArea.value.x &&
    x <= cropArea.value.x + cropArea.value.width &&
    y >= cropArea.value.y &&
    y <= cropArea.value.y + cropArea.value.height
  ) {
    isDragging.value = true;
    dragStart.value = { x: x - cropArea.value.x, y: y - cropArea.value.y };
    canvas.style.cursor = 'grabbing';
  }
};

// 拖拽中
const onDrag = (e: MouseEvent) => {
  if (!isDragging.value) return;

  const canvas = cropCanvas.value;
  if (!canvas) return;

  const rect = canvas.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  cropArea.value.x = Math.max(
    0,
    Math.min(x - dragStart.value.x, canvas.width - cropArea.value.width),
  );
  cropArea.value.y = Math.max(
    0,
    Math.min(y - dragStart.value.y, canvas.height - cropArea.value.height),
  );

  drawCanvas();
};

// 鼠标移动时更新光标
const onMouseMove = (e: MouseEvent) => {
  if (isDragging.value) return;

  const canvas = cropCanvas.value;
  if (!canvas) return;

  const rect = canvas.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  // 检查是否在裁剪区域内
  if (
    x >= cropArea.value.x &&
    x <= cropArea.value.x + cropArea.value.width &&
    y >= cropArea.value.y &&
    y <= cropArea.value.y + cropArea.value.height
  ) {
    canvas.style.cursor = 'grab';
  } else {
    canvas.style.cursor = 'default';
  }
};

// 结束拖拽
const endDrag = () => {
  isDragging.value = false;
  const canvas = cropCanvas.value;
  if (canvas) {
    canvas.style.cursor = 'default';
  }
};

// 缩放控制
const handleScaleChange = (value: number) => {
  scale.value = value;
  drawCanvas();
};

// 旋转控制
const handleRotate = () => {
  rotation.value = (rotation.value + 90) % 360;
  drawCanvas();
};

// 重置变换
const resetTransform = () => {
  scale.value = 1;
  rotation.value = 0;
  drawCanvas();
};

// 确认裁剪
const confirmCrop = async () => {
  if (!originalFile.value || !originalImage.value) return;

  try {
    loading.value = true;

    // 创建裁剪后的图片
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置输出尺寸为固定的头像尺寸
    const outputSize = 300; // 高质量输出
    canvas.width = outputSize;
    canvas.height = outputSize;

    // 应用变换并绘制
    ctx.save();

    // 计算缩放比例
    const scaleRatio = outputSize / cropArea.value.width;

    ctx.translate(outputSize / 2, outputSize / 2);
    ctx.scale(scale.value * scaleRatio, scale.value * scaleRatio);
    ctx.rotate((rotation.value * Math.PI) / 180);

    // 绘制图片
    const sourceX = cropArea.value.x / scale.value;
    const sourceY = cropArea.value.y / scale.value;
    const sourceWidth = cropArea.value.width / scale.value;
    const sourceHeight = cropArea.value.height / scale.value;

    ctx.drawImage(
      originalImage.value,
      sourceX,
      sourceY,
      sourceWidth,
      sourceHeight,
      -outputSize / 2,
      -outputSize / 2,
      outputSize,
      outputSize,
    );

    ctx.restore();

    // 转换为Blob
    canvas.toBlob(async (blob) => {
      if (!blob) return;

      const croppedFile = new File([blob], originalFile.value!.name, {
        type: originalFile.value!.type,
      });

      try {
        const response = await FileApi.uploadImage(croppedFile);
        if (response.fileUrl) {
          imageUrl.value = response.fileUrl;
          emit('update:value', response.fileUrl);

          const fileResponse: ImageUploadResponse = {
            fileName: response.fileName,
            fileSize: String(response.fileSize || ''),
            fileUrl: response.fileUrl,
            previewUrl: response.previewUrl || response.fileUrl,
            fileType: response.fileType,
          };
          emit('success', fileResponse);
          message.success('头像上传成功');
          showCropModal.value = false;
        }
      } catch (error) {
        console.error('Upload failed:', error);
        message.error('头像上传失败，请重试');
      }
    }, originalFile.value.type, 0.9); // 高质量输出
  } finally {
    loading.value = false;
  }
};

// 取消裁剪
const cancelCrop = () => {
  showCropModal.value = false;
  originalFile.value = undefined;
  originalImage.value = undefined;
  scale.value = 1;
  rotation.value = 0;
};
</script>

<template>
  <div class="upload-avatar">
    <Upload
      :custom-request="customRequest"
      :disabled="props.disabled"
      :show-upload-list="false"
      accept="image/*"
      class="avatar-uploader"
    >
      <div :style="avatarStyle" class="upload-area" :class="{ disabled: props.disabled }">
        <div v-if="imageUrl" class="avatar-preview">
          <img :src="imageUrl" alt="avatar" />
          <div class="avatar-overlay">
            <div class="overlay-icon">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div class="overlay-text">更换头像</div>
          </div>
        </div>
        <div v-else class="upload-placeholder">
          <div class="upload-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </div>
          <div class="upload-text">上传头像</div>
        </div>
      </div>
    </Upload>

    <!-- 裁剪弹窗 -->
    <Modal
      v-model:open="showCropModal"
      title="编辑头像"
      :width="800"
      :confirm-loading="loading"
      ok-text="确认上传"
      cancel-text="取消"
      @ok="confirmCrop"
      @cancel="cancelCrop"
    >
      <div class="crop-modal-content">
        <!-- 工具栏 -->
        <div class="crop-toolbar">
          <div class="toolbar-section">
            <span class="toolbar-label">缩放:</span>
            <Slider
              v-model:value="scale"
              :min="minScale"
              :max="maxScale"
              :step="0.1"
              :tooltip-formatter="(value) => `${Math.round(value * 100)}%`"
              class="scale-slider"
              @change="handleScaleChange"
            />
          </div>
          <div class="toolbar-actions">
            <Button size="small" @click="handleRotate">
              <template #icon>
                <RedoOutlined />
              </template>
              旋转
            </Button>
            <Button size="small" @click="resetTransform">
              重置
            </Button>
          </div>
        </div>

        <!-- 编辑区域 -->
        <div class="crop-container">
          <div class="crop-area">
            <div class="crop-canvas-wrapper">
              <canvas
                ref="cropCanvas"
                @mousedown="startDrag"
                @mousemove="onMouseMove"
                @mouseup="endDrag"
                @mouseleave="endDrag"
                @wheel="handleWheel"
              ></canvas>
            </div>
            <div class="crop-tips">
              <p>• 拖拽选框调整裁剪区域</p>
              <p>• 使用鼠标滚轮缩放图片</p>
              <p>• 点击旋转按钮调整角度</p>
            </div>
          </div>

          <div class="preview-area">
            <div class="preview-title">头像预览</div>
            <div class="preview-circle">
              <canvas ref="previewCanvas" width="150" height="150"></canvas>
            </div>
            <div class="preview-sizes">
              <div class="size-preview">
                <div class="size-label">大尺寸</div>
                <div class="size-avatar size-large">
                  <canvas width="80" height="80"></canvas>
                </div>
              </div>
              <div class="size-preview">
                <div class="size-label">中尺寸</div>
                <div class="size-avatar size-medium">
                  <canvas width="50" height="50"></canvas>
                </div>
              </div>
              <div class="size-preview">
                <div class="size-label">小尺寸</div>
                <div class="size-avatar size-small">
                  <canvas width="32" height="32"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.upload-avatar {
  display: inline-block;
}

.avatar-uploader {
  display: block;
}

.upload-area {
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border: 2px dashed #d9d9d9;
  position: relative;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
  transform: scale(1.02);
}

.upload-area.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-area.disabled:hover {
  border-color: #d9d9d9;
  background: #fafafa;
  transform: none;
}

.avatar-preview {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
}

.avatar-preview:hover .avatar-overlay {
  opacity: 1;
}

.overlay-icon {
  margin-bottom: 4px;
}

.overlay-text {
  font-size: 12px;
  font-weight: 500;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  text-align: center;
}

.upload-icon {
  margin-bottom: 8px;
  color: #1890ff;
}

.upload-text {
  font-size: 14px;
  font-weight: 500;
}

/* 裁剪弹窗样式 */
.crop-modal-content {
  padding: 16px 0;
}

.crop-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.toolbar-label {
  font-weight: 500;
  color: #666;
  min-width: 40px;
}

.scale-slider {
  flex: 1;
  max-width: 200px;
}

.toolbar-actions {
  display: flex;
  gap: 8px;
}

.crop-container {
  display: flex;
  gap: 24px;
  min-height: 400px;
}

.crop-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.crop-canvas-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  padding: 20px;
  min-height: 350px;
}

.crop-canvas-wrapper canvas {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.crop-tips {
  margin-top: 16px;
  padding: 12px;
  background: #f6f8fa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.crop-tips p {
  margin: 4px 0;
  font-size: 13px;
  color: #666;
}

.preview-area {
  width: 200px;
  text-align: center;
  display: flex;
  flex-direction: column;
}

.preview-title {
  margin-bottom: 16px;
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.preview-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e9ecef;
  margin: 0 auto 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
}

.preview-circle canvas {
  display: block;
}

.preview-sizes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.size-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.size-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.size-avatar {
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  background: white;
}

.size-avatar canvas {
  display: block;
}

.size-large {
  width: 40px;
  height: 40px;
}

.size-medium {
  width: 32px;
  height: 32px;
}

.size-small {
  width: 24px;
  height: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .crop-container {
    flex-direction: column;
    gap: 16px;
  }

  .preview-area {
    width: 100%;
  }

  .preview-sizes {
    flex-direction: row;
    justify-content: center;
    gap: 16px;
  }

  .size-preview {
    flex-direction: column;
    gap: 4px;
    text-align: center;
    min-width: 60px;
  }

  .crop-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-section {
    justify-content: center;
  }

  .toolbar-actions {
    justify-content: center;
  }
}
</style>
