<script setup lang="ts">
import type { FileUploadResponse, ImageUploadResponse } from './types';

import { computed, ref } from 'vue';

import { Button, message, Modal, Slider, Space, Upload } from 'ant-design-vue';
import { VueCropper } from 'vue-cropper';
import 'vue-cropper/next/dist/index.css';

import { FileApi } from '#/api/common/file';

interface Props {
  value?: string;
  size?: number;
}

interface Emits {
  (e: 'success', data: FileUploadResponse): void;
  (e: 'update:value', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  size: 120
});
const emit = defineEmits<Emits>();

const loading = ref(false);
const imageUrl = ref<string>(props.value || '');

// vue-cropper 相关状态
const showCropModal = ref(false);
const cropperRef = ref();
const originalImageSrc = ref<string>('');
const cropperScale = ref(1);
const originalFile = ref<File>();

// 计算属性
const avatarSize = computed(() => `${props.size}px`);
const previewSize = computed(() => Math.min(props.size * 2, 200));

// 自定义上传函数
const customRequest = (options: any) => {
  const { file } = options;
  originalFile.value = file;

  // 创建图片预览并显示裁剪弹窗
  const reader = new FileReader();
  reader.addEventListener('load', (e) => {
    originalImageSrc.value = e.target?.result as string;
    showCropModal.value = true;
  });
  reader.readAsDataURL(file);
};

// 缩放控制
const handleScaleChange = (value: number) => {
  cropperScale.value = value;
  if (cropperRef.value) {
    cropperRef.value.changeScale(value);
  }
};

// 鼠标滚轮缩放
const handleWheel = (e: WheelEvent) => {
  e.preventDefault();
  const delta = e.deltaY > 0 ? -0.1 : 0.1;
  const newScale = Math.max(0.1, Math.min(3, cropperScale.value + delta));
  handleScaleChange(newScale);
};

// 旋转控制
const rotateLeft = () => {
  if (cropperRef.value) {
    cropperRef.value.rotateLeft();
  }
};

const rotateRight = () => {
  if (cropperRef.value) {
    cropperRef.value.rotateRight();
  }
};

// 重置
const resetCropper = () => {
  if (cropperRef.value) {
    cropperRef.value.refresh();
    cropperScale.value = 1;
  }
};

// 确认裁剪
const confirmCrop = async () => {
  if (!originalFile.value || !cropperRef.value) return;

  try {
    loading.value = true;

    // 获取裁剪后的图片数据
    cropperRef.value.getCropBlob(async (blob: Blob) => {
      if (!blob) return;

      const croppedFile = new File([blob], originalFile.value!.name, {
        type: originalFile.value!.type,
      });

      try {
        const response = await FileApi.uploadImage(croppedFile);
        if (response.fileUrl) {
          imageUrl.value = response.fileUrl;
          emit('update:value', response.fileUrl);

          const fileResponse: ImageUploadResponse = {
            fileName: response.fileName,
            fileSize: String(response.fileSize || ''),
            fileUrl: response.fileUrl,
            fileType: response.fileType,
          };
          emit('success', fileResponse);
          message.success('头像上传成功');
          showCropModal.value = false;
        }
      } catch (error) {
        console.error('Upload failed:', error);
        message.error('头像上传失败');
      } finally {
        loading.value = false;
      }
    });
  } catch (error) {
    console.error('Crop failed:', error);
    message.error('图片裁剪失败');
    loading.value = false;
  }
};

// 取消裁剪
const cancelCrop = () => {
  showCropModal.value = false;
  originalFile.value = undefined;
  originalImageSrc.value = '';
  cropperScale.value = 1;
};
</script>

<template>
  <div class="upload-avatar">
    <Upload
      :custom-request="customRequest"
      :show-upload-list="false"
      accept="image/*"
      class="avatar-uploader"
    >
      <div class="upload-area" :style="{ width: avatarSize, height: avatarSize }">
        <div v-if="imageUrl" class="avatar-preview">
          <img :src="imageUrl" alt="avatar" />
        </div>
        <div v-else class="upload-placeholder">
          <div class="upload-icon">+</div>
          <div class="upload-text">上传头像</div>
        </div>
      </div>
    </Upload>

    <!-- 裁剪弹窗 -->
    <Modal
      v-model:open="showCropModal"
      title="裁剪头像"
      :width="900"
      :footer="null"
      class="crop-modal"
      @cancel="cancelCrop"
    >
      <div class="crop-container">
        <!-- 左侧裁剪区域 -->
        <div class="crop-main">
          <div class="crop-wrapper" @wheel="handleWheel">
            <VueCropper
              ref="cropperRef"
              :img="originalImageSrc"
              :output-size="1"
              :output-type="'png'"
              :info="true"
              :full="false"
              :can-move="true"
              :can-move-box="true"
              :original="false"
              :auto-crop="true"
              :auto-crop-width="200"
              :auto-crop-height="200"
              :center-box="true"
              :high="true"
              :fixed="true"
              :fixed-number="[1, 1]"
              :max-img-size="3000"
              :enlarge="1"
              :mode="'contain'"
              class="vue-cropper"
            />
          </div>
          
          <!-- 控制按钮 -->
          <div class="crop-controls">
            <Space size="middle">
              <Button @click="rotateLeft">向左旋转</Button>
              <Button @click="rotateRight">向右旋转</Button>
              <Button @click="resetCropper">重置</Button>
            </Space>
            
            <div class="scale-control">
              <span class="scale-label">缩放:</span>
              <Slider
                v-model:value="cropperScale"
                :min="0.1"
                :max="3"
                :step="0.1"
                :style="{ width: '120px' }"
                @change="(value: number) => handleScaleChange(value)"
              />
              <span class="scale-value">{{ Math.round(cropperScale * 100) }}%</span>
            </div>
          </div>
        </div>
        
        <!-- 右侧预览区域 -->
        <div class="preview-section">
          <div class="preview-title">预览效果</div>
          <div class="preview-container">
            <div 
              class="preview-circle large"
              :style="{ width: `${previewSize}px`, height: `${previewSize}px` }"
            >
              <VueCropper
                :img="originalImageSrc"
                :output-size="1"
                :output-type="'png'"
                :info="false"
                :full="false"
                :can-move="false"
                :can-move-box="false"
                :original="false"
                :auto-crop="true"
                :auto-crop-width="200"
                :auto-crop-height="200"
                :center-box="true"
                :high="true"
                :fixed="true"
                :fixed-number="[1, 1]"
                :enlarge="1"
                :mode="'contain'"
                class="preview-cropper"
              />
            </div>
            
            <div class="preview-sizes">
              <div class="size-item">
                <div class="preview-circle small">
                  <VueCropper
                    :img="originalImageSrc"
                    :output-size="1"
                    :output-type="'png'"
                    :info="false"
                    :full="false"
                    :can-move="false"
                    :can-move-box="false"
                    :original="false"
                    :auto-crop="true"
                    :auto-crop-width="200"
                    :auto-crop-height="200"
                    :center-box="true"
                    :high="true"
                    :fixed="true"
                    :fixed-number="[1, 1]"
                    :enlarge="1"
                    :mode="'contain'"
                    class="preview-cropper"
                  />
                </div>
                <span>小尺寸</span>
              </div>
              
              <div class="size-item">
                <div class="preview-circle medium">
                  <VueCropper
                    :img="originalImageSrc"
                    :output-size="1"
                    :output-type="'png'"
                    :info="false"
                    :full="false"
                    :can-move="false"
                    :can-move-box="false"
                    :original="false"
                    :auto-crop="true"
                    :auto-crop-width="200"
                    :auto-crop-height="200"
                    :center-box="true"
                    :high="true"
                    :fixed="true"
                    :fixed-number="[1, 1]"
                    :enlarge="1"
                    :mode="'contain'"
                    class="preview-cropper"
                  />
                </div>
                <span>中等尺寸</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作按钮 -->
      <div class="modal-footer">
        <Space>
          <Button @click="cancelCrop">取消</Button>
          <Button type="primary" :loading="loading" @click="confirmCrop">
            确认上传
          </Button>
        </Space>
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.upload-avatar {
  display: inline-block;
}

.avatar-uploader {
  display: block;
}

.upload-area {
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border: 1px solid #d9d9d9;
}

.upload-area:hover {
  border-color: #1890ff;
}

.avatar-preview {
  width: 100%;
  height: 100%;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
}

/* 裁剪弹窗样式 */
.crop-modal :deep(.ant-modal-body) {
  padding: 24px;
}

.crop-container {
  display: flex;
  gap: 24px;
  min-height: 500px;
}

.crop-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.crop-wrapper {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  position: relative;
  min-height: 400px;
}

.vue-cropper {
  width: 100%;
  height: 100%;
}

.crop-controls {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.scale-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scale-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.scale-value {
  font-size: 12px;
  color: #999;
  min-width: 40px;
  text-align: center;
}

/* 预览区域样式 */
.preview-section {
  width: 280px;
  display: flex;
  flex-direction: column;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  text-align: center;
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.preview-circle {
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e8e8e8;
  background: #fff;
  position: relative;
}

.preview-circle.large {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-circle.small {
  width: 60px;
  height: 60px;
}

.preview-circle.medium {
  width: 80px;
  height: 80px;
}

.preview-cropper {
  width: 100%;
  height: 100%;
}

.preview-sizes {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.size-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.size-item span {
  font-size: 12px;
  color: #666;
}

/* 底部按钮样式 */
.modal-footer {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .crop-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .preview-section {
    width: 100%;
  }
  
  .crop-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .scale-control {
    justify-content: center;
  }
}
</style>
