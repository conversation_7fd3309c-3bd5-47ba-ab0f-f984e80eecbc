# Avatar Upload Component - Vue-Cropper 重构版本

## 概述
头像上传组件已使用 `vue-cropper` 进行完全重构，提供更专业、更稳定的图片裁剪功能，同时保持与 echoPro 项目的完全兼容性。

## 主要改进

### 🔄 技术栈升级
- **替换自定义 Canvas 实现** → **vue-cropper 专业裁剪库**
- **更稳定的裁剪算法** → 经过大量项目验证的成熟方案
- **更好的浏览器兼容性** → 支持更多设备和浏览器
- **内存管理优化** → 自动处理资源清理，避免内存泄漏

### ✨ 功能增强

#### 1. 专业裁剪工具
```typescript
// vue-cropper 配置
const cropperOptions = ref({
  outputSize: 1,           // 高质量输出
  outputType: 'jpeg',      // 输出格式
  canScale: true,          // 支持缩放
  autoCrop: true,          // 自动裁剪
  fixed: true,             // 固定比例
  fixedNumber: [1, 1],     // 1:1 比例
  centerBox: true,         // 居中限制
  high: true,              // 高清输出
});
```

#### 2. 丰富的控制选项
- **放大/缩小**: 精确的缩放控制
- **左转/右转**: 90度旋转功能
- **重置**: 一键恢复初始状态
- **实时预览**: 即时查看裁剪效果

#### 3. 优化的用户界面
- **更大的编辑区域**: 400px 高度的裁剪空间
- **清晰的控制按钮**: 图标+文字的直观操作
- **实时预览**: 圆形头像预览效果
- **响应式布局**: 移动端友好设计

## 核心功能

### 图片裁剪
```vue
<VueCropper
  ref="cropper"
  :img="cropperOptions.img"
  :auto-crop="true"
  :auto-crop-width="200"
  :auto-crop-height="200"
  :fixed="true"
  :fixed-number="[1, 1]"
  @real-time="handleRealTime"
/>
```

### 控制操作
```typescript
// 缩放控制
const handleZoomIn = () => cropper.value?.changeScale(1);
const handleZoomOut = () => cropper.value?.changeScale(-1);

// 旋转控制
const handleRotateLeft = () => cropper.value?.rotateLeft();
const handleRotateRight = () => cropper.value?.rotateRight();

// 重置
const handleReset = () => cropper.value?.refresh();
```

### 图片上传
```typescript
const confirmCrop = async () => {
  cropper.value.getCropBlob(async (blob: Blob) => {
    const croppedFile = new File([blob], originalFile.value!.name, {
      type: originalFile.value!.type,
    });
    
    const response = await FileApi.uploadImage(croppedFile);
    // 处理上传结果...
  });
};
```

## 组件 API

### Props
```typescript
interface Props {
  value?: string;           // 头像 URL
  size?: number;           // 头像显示尺寸 (默认: 100px)
  disabled?: boolean;      // 是否禁用 (默认: false)
}
```

### Events
```typescript
interface Emits {
  (e: 'success', data: FileUploadResponse): void;
  (e: 'update:value', value: string): void;
}
```

## 使用示例

### 基础用法
```vue
<template>
  <UploadAvatar 
    v-model:value="avatarUrl"
    @success="handleSuccess"
  />
</template>

<script setup>
import UploadAvatar from '@/components/upload/UploadAvatar.vue';

const avatarUrl = ref('');

const handleSuccess = (response) => {
  console.log('头像上传成功:', response);
};
</script>
```

### 自定义尺寸
```vue
<UploadAvatar 
  v-model:value="avatarUrl"
  :size="150"
  @success="handleSuccess"
/>
```

### 禁用状态
```vue
<UploadAvatar 
  v-model:value="avatarUrl"
  :disabled="true"
/>
```

## 技术优势

### 1. 稳定性提升
- 使用经过验证的 vue-cropper 库
- 减少自定义代码的潜在 bug
- 更好的错误处理机制

### 2. 性能优化
- 高效的图片处理算法
- 优化的内存使用
- 自动资源清理

### 3. 用户体验
- 更直观的操作界面
- 丰富的控制选项
- 实时预览反馈

### 4. 维护性
- 减少自定义代码量
- 依赖成熟的开源库
- 更容易升级和维护

## 兼容性

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

### 项目集成
- ✅ Vue 3 + TypeScript
- ✅ Ant Design Vue
- ✅ Tailwind CSS
- ✅ echoPro 项目规范

## 文件结构
```
components/upload/
├── UploadAvatar.vue          # 主组件 (vue-cropper版本)
├── UploadAvatarDemo.vue      # 演示页面
├── types.ts                  # 类型定义
└── AVATAR_UPLOAD_VUE_CROPPER.md  # 文档
```

## 依赖
```json
{
  "vue-cropper": "^1.0.3"
}
```

这个重构版本提供了更专业、更稳定的头像上传和裁剪功能，同时保持了简洁的 API 和良好的用户体验。
